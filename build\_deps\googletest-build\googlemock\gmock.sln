﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{6D0E08A3-B929-3052-816B-C226C9760A5B}"
	ProjectSection(ProjectDependencies) = postProject
		{58B244FA-5339-396D-8649-06E194B8647E} = {58B244FA-5339-396D-8649-06E194B8647E}
		{30E11BAC-D033-3771-82B7-9F992E0C6519} = {30E11BAC-D033-3771-82B7-9F992E0C6519}
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7} = {4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23} = {0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}
		{D05585A4-DFB8-30D4-9670-568840C7F622} = {D05585A4-DFB8-30D4-9670-568840C7F622}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{6977FA27-41D9-3062-BB7F-288CF5B94B8B}"
	ProjectSection(ProjectDependencies) = postProject
		{6D0E08A3-B929-3052-816B-C226C9760A5B} = {6D0E08A3-B929-3052-816B-C226C9760A5B}
		{58B244FA-5339-396D-8649-06E194B8647E} = {58B244FA-5339-396D-8649-06E194B8647E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{20B6FE99-EC18-302E-A314-304ED747D0B9}"
	ProjectSection(ProjectDependencies) = postProject
		{58B244FA-5339-396D-8649-06E194B8647E} = {58B244FA-5339-396D-8649-06E194B8647E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\..\\ZERO_CHECK.vcxproj", "{58B244FA-5339-396D-8649-06E194B8647E}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "gmock", "gmock.vcxproj", "{30E11BAC-D033-3771-82B7-9F992E0C6519}"
	ProjectSection(ProjectDependencies) = postProject
		{58B244FA-5339-396D-8649-06E194B8647E} = {58B244FA-5339-396D-8649-06E194B8647E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "gmock_main", "gmock_main.vcxproj", "{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}"
	ProjectSection(ProjectDependencies) = postProject
		{58B244FA-5339-396D-8649-06E194B8647E} = {58B244FA-5339-396D-8649-06E194B8647E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "gtest", "..\googletest\gtest.vcxproj", "{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}"
	ProjectSection(ProjectDependencies) = postProject
		{58B244FA-5339-396D-8649-06E194B8647E} = {58B244FA-5339-396D-8649-06E194B8647E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "gtest_main", "..\googletest\gtest_main.vcxproj", "{D05585A4-DFB8-30D4-9670-568840C7F622}"
	ProjectSection(ProjectDependencies) = postProject
		{58B244FA-5339-396D-8649-06E194B8647E} = {58B244FA-5339-396D-8649-06E194B8647E}
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23} = {0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6D0E08A3-B929-3052-816B-C226C9760A5B}.Debug|x64.ActiveCfg = Debug|x64
		{6D0E08A3-B929-3052-816B-C226C9760A5B}.Debug|x64.Build.0 = Debug|x64
		{6D0E08A3-B929-3052-816B-C226C9760A5B}.Release|x64.ActiveCfg = Release|x64
		{6D0E08A3-B929-3052-816B-C226C9760A5B}.Release|x64.Build.0 = Release|x64
		{6D0E08A3-B929-3052-816B-C226C9760A5B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6D0E08A3-B929-3052-816B-C226C9760A5B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6D0E08A3-B929-3052-816B-C226C9760A5B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6D0E08A3-B929-3052-816B-C226C9760A5B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6977FA27-41D9-3062-BB7F-288CF5B94B8B}.Debug|x64.ActiveCfg = Debug|x64
		{6977FA27-41D9-3062-BB7F-288CF5B94B8B}.Release|x64.ActiveCfg = Release|x64
		{6977FA27-41D9-3062-BB7F-288CF5B94B8B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6977FA27-41D9-3062-BB7F-288CF5B94B8B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{20B6FE99-EC18-302E-A314-304ED747D0B9}.Debug|x64.ActiveCfg = Debug|x64
		{20B6FE99-EC18-302E-A314-304ED747D0B9}.Release|x64.ActiveCfg = Release|x64
		{20B6FE99-EC18-302E-A314-304ED747D0B9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{20B6FE99-EC18-302E-A314-304ED747D0B9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{58B244FA-5339-396D-8649-06E194B8647E}.Debug|x64.ActiveCfg = Debug|x64
		{58B244FA-5339-396D-8649-06E194B8647E}.Debug|x64.Build.0 = Debug|x64
		{58B244FA-5339-396D-8649-06E194B8647E}.Release|x64.ActiveCfg = Release|x64
		{58B244FA-5339-396D-8649-06E194B8647E}.Release|x64.Build.0 = Release|x64
		{58B244FA-5339-396D-8649-06E194B8647E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{58B244FA-5339-396D-8649-06E194B8647E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{58B244FA-5339-396D-8649-06E194B8647E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{58B244FA-5339-396D-8649-06E194B8647E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{30E11BAC-D033-3771-82B7-9F992E0C6519}.Debug|x64.ActiveCfg = Debug|x64
		{30E11BAC-D033-3771-82B7-9F992E0C6519}.Debug|x64.Build.0 = Debug|x64
		{30E11BAC-D033-3771-82B7-9F992E0C6519}.Release|x64.ActiveCfg = Release|x64
		{30E11BAC-D033-3771-82B7-9F992E0C6519}.Release|x64.Build.0 = Release|x64
		{30E11BAC-D033-3771-82B7-9F992E0C6519}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{30E11BAC-D033-3771-82B7-9F992E0C6519}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{30E11BAC-D033-3771-82B7-9F992E0C6519}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{30E11BAC-D033-3771-82B7-9F992E0C6519}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}.Debug|x64.ActiveCfg = Debug|x64
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}.Debug|x64.Build.0 = Debug|x64
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}.Release|x64.ActiveCfg = Release|x64
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}.Release|x64.Build.0 = Release|x64
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4CFCAC00-3BE1-3485-B87C-F023DE1CB7E7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}.Debug|x64.ActiveCfg = Debug|x64
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}.Debug|x64.Build.0 = Debug|x64
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}.Release|x64.ActiveCfg = Release|x64
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}.Release|x64.Build.0 = Release|x64
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{0A480B6A-E7CC-3B29-AA5E-D590CC3F8E23}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D05585A4-DFB8-30D4-9670-568840C7F622}.Debug|x64.ActiveCfg = Debug|x64
		{D05585A4-DFB8-30D4-9670-568840C7F622}.Debug|x64.Build.0 = Debug|x64
		{D05585A4-DFB8-30D4-9670-568840C7F622}.Release|x64.ActiveCfg = Release|x64
		{D05585A4-DFB8-30D4-9670-568840C7F622}.Release|x64.Build.0 = Release|x64
		{D05585A4-DFB8-30D4-9670-568840C7F622}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D05585A4-DFB8-30D4-9670-568840C7F622}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D05585A4-DFB8-30D4-9670-568840C7F622}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D05585A4-DFB8-30D4-9670-568840C7F622}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6D6D2E87-9582-3F85-8B7F-4064D9E34CC0}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
