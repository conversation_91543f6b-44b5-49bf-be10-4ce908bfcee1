﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\CMakeFiles\3.28.1\CompilerIdCXX\CMakeCXXCompilerId.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\builder\delete_builder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\builder\insert_builder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\builder\select_builder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\builder\sql_builder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\builder\sql_condition.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\builder\update_builder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\connection\connection_params.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\connection\connection_pool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\connection\connection_pool_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\driver\sql_field.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\driver\sql_record.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\driver\sql_result.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\driver\sql_statement.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\driver\sqlite\sqlite_driver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\driver\sqlite\sqlite_factory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\driver\sqlite\sqlite_statement.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\exception\sql_error.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\exception\sql_exception.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\sql_database.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\sql_query.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{6499DCDF-FE9A-3BEC-A12C-E6B2EDA78214}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
