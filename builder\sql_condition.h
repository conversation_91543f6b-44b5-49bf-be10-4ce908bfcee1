﻿#ifndef DATABASE_SQL_CONDITION_H
#define DATABASE_SQL_CONDITION_H

#include <format>
#include <string>
#include <string_view>
#include <vector>
#include <unordered_map>
#include <sstream>

#include "variant.h"
#include "sql_types.h"
#include "sql_column.h"

namespace database {

/**
 * @brief Enumeration of parameter placeholder styles
 */
enum class ParameterStyle {
    QuestionMark,    ///< ? style (positional)
    NamedColon,      ///< :name style (named)
    NamedAt,         ///< @name style (named)
    IndexedDollar    ///< $1 style (indexed)
};

/**
 * @brief Class representing an SQL condition
 *
 * This class encapsulates an SQL condition that can be used in WHERE clauses.
 * It supports various comparison operators and logical operators.
 */
class SqlCondition {
public:
    /**
     * @brief Default constructor
     */
    SqlCondition();

    /**
     * @brief Set the parameter style for all new instances
     * @param style The parameter style to use
     */
    static void setDefaultParameterStyle(ParameterStyle style);

    /**
     * @brief Get the current default parameter style
     * @return The current default parameter style
     */
    static ParameterStyle defaultParameterStyle();

    /**
     * @brief Set the parameter style for this instance
     * @param style The parameter style to use
     * @return Reference to this condition for method chaining
     */
    SqlCondition& setParameterStyle(ParameterStyle style);

    /**
     * @brief Get the parameter style for this instance
     * @return The parameter style
     */
    [[nodiscard]] ParameterStyle parameterStyle() const noexcept;

    /**
     * @brief Constructor with column, operator, and value
     * @param column The column
     * @param op The operator
     * @param value The value
     */
    SqlCondition(const SqlColumn& column, SqlOperator op, const Variant& value);

    /**
     * @brief Constructor with column, string operator, and value
     * @param column The column
     * @param opStr The operator as a string (e.g., "=", "<>", "LIKE", etc.)
     * @param value The value
     */
    SqlCondition(const SqlColumn& column, std::string_view opStr, const Variant& value);

    /**
     * @brief Constructor with column and operator (for unary operators like IS NULL)
     * @param column The column
     * @param op The operator
     */
    SqlCondition(const SqlColumn& column, SqlOperator op);

    /**
     * @brief Constructor with column and string operator (for unary operators like IS NULL)
     * @param column The column
     * @param opStr The operator as a string (e.g., "IS NULL", "IS NOT NULL")
     */
    SqlCondition(const SqlColumn& column, std::string_view opStr);

    /**
     * @brief Constructor with column, operator, and multiple values (for IN, BETWEEN)
     * @param column The column
     * @param op The operator
     * @param values The values
     */
    SqlCondition(const SqlColumn& column, SqlOperator op, const std::vector<Variant>& values);

    /**
     * @brief Constructor with column, string operator, and multiple values (for IN, BETWEEN)
     * @param column The column
     * @param opStr The operator as a string (e.g., "IN", "NOT IN", "BETWEEN")
     * @param values The values
     */
    SqlCondition(const SqlColumn& column, std::string_view opStr, const std::vector<Variant>& values);

    /**
     * @brief Constructor with column, operator, and variadic values (for IN)
     * @tparam Args Types of the values (must be convertible to Variant)
     * @param column The column
     * @param op The operator
     * @param values The values
     */
    template<typename... Args>
    SqlCondition(const SqlColumn& column, SqlOperator op, Args&&... values) {
        if constexpr (sizeof...(values) > 0) {
            std::vector<Variant> valueVector;
            valueVector.reserve(sizeof...(values));
            (valueVector.push_back(Variant(std::forward<Args>(values))), ...);

            if (op == SqlOperator::In || op == SqlOperator::NotIn) {
                if (valueVector.empty()) {
                    // Empty IN clause is always false
                    m_condition = op == SqlOperator::In ? "0 = 1" : "1 = 1";
                    return;
                }

                std::ostringstream inClause;
                inClause << column.qualifiedName() << " " << sqlOperatorToString(op) << " (";

                for (size_t i = 0; i < valueVector.size(); ++i) {
                    if (i > 0) {
                        inClause << ", ";
                    }

                    std::string paramName = addParameter(valueVector[i]);
                    inClause << paramName;
                }

                inClause << ")";
                m_condition = inClause.str();
            } else if (op == SqlOperator::Between && valueVector.size() >= 2) {
                std::string minParamName = addParameter(valueVector[0]);
                std::string maxParamName = addParameter(valueVector[1]);

                m_condition = std::format("{} {} {} AND {}",
                                          column.qualifiedName(),
                                          sqlOperatorToString(op),
                                          minParamName,
                                          maxParamName);
            }
        }
    }

    /**
     * @brief Constructor with column, string operator, and variadic values (for IN)
     * @tparam Args Types of the values (must be convertible to Variant)
     * @param column The column
     * @param opStr The operator as a string (e.g., "IN", "NOT IN", "BETWEEN")
     * @param values The values
     */
    template<typename... Args>
    SqlCondition(const SqlColumn& column, std::string_view opStr, Args&&... values) {
        // Convert the string operator to SqlOperator enum
        SqlOperator op = stringToSqlOperator(opStr);

        // Use the same implementation as the SqlOperator version
        if constexpr (sizeof...(values) > 0) {
            std::vector<Variant> valueVector;
            valueVector.reserve(sizeof...(values));
            (valueVector.push_back(Variant(std::forward<Args>(values))), ...);

            if (op == SqlOperator::In || op == SqlOperator::NotIn) {
                if (valueVector.empty()) {
                    // Empty IN clause is always false
                    m_condition = op == SqlOperator::In ? "0 = 1" : "1 = 1";
                    return;
                }

                std::ostringstream inClause;
                inClause << column.qualifiedName() << " " << sqlOperatorToString(op) << " (";

                for (size_t i = 0; i < valueVector.size(); ++i) {
                    if (i > 0) {
                        inClause << ", ";
                    }

                    std::string paramName = addParameter(valueVector[i]);
                    inClause << paramName;
                }

                inClause << ")";
                m_condition = inClause.str();
            } else if (op == SqlOperator::Between && valueVector.size() >= 2) {
                std::string minParamName = addParameter(valueVector[0]);
                std::string maxParamName = addParameter(valueVector[1]);

                m_condition = std::format("{} {} {} AND {}",
                                          column.qualifiedName(),
                                          sqlOperatorToString(op),
                                          minParamName,
                                          maxParamName);
            }
        }
    }

    /**
     * @brief Constructor with raw SQL condition
     * @param condition The raw SQL condition
     */
    explicit SqlCondition(std::string_view condition);

    /**
     * @brief Combine with another condition using AND
     * @param other The other condition
     * @return A new condition representing the AND combination
     */
    [[nodiscard]] SqlCondition operator&&(const SqlCondition& other) const;

    /**
     * @brief Combine with another condition using OR
     * @param other The other condition
     * @return A new condition representing the OR combination
     */
    [[nodiscard]] SqlCondition operator||(const SqlCondition& other) const;

    /**
     * @brief Negate the condition
     * @return A new condition representing the negation
     */
    [[nodiscard]] SqlCondition operator!() const;

    /**
     * @brief Get the SQL representation of the condition
     * @return The SQL string
     */
    [[nodiscard]] std::string toSql() const;

    /**
     * @brief Get the parameters for this condition
     * @return The parameter vector
     */
    [[nodiscard]] const std::vector<Variant>& parameters() const noexcept { return m_paramValues; }

    /**
     * @brief Get the named parameters for this condition
     * @return The parameter map
     */
    [[nodiscard]] const std::unordered_map<std::string, Variant>& namedParameters() const noexcept { return m_namedParams; }

    /**
     * @brief Check if the condition is empty
     * @return True if the condition is empty, false otherwise
     */
    [[nodiscard]] bool isEmpty() const noexcept { return m_condition.empty(); }

private:
    /**
     * @brief Add a parameter to the parameter storage
     * @param value The parameter value
     * @return The parameter placeholder based on the current style
     */
    std::string addParameter(const Variant& value);

    /**
     * @brief Add a parameter with a specific name
     * @param name The parameter name
     * @param value The parameter value
     * @return The parameter placeholder based on the current style
     */
    std::string addNamedParameter(std::string_view name, const Variant& value);

    /**
     * @brief Generate a unique parameter name
     * @return A unique parameter name
     */
    [[nodiscard]] std::string generateParamName() const;

    /**
     * @brief Format a parameter placeholder based on the current style
     * @param name The parameter name
     * @return The formatted placeholder
     */
    [[nodiscard]] std::string formatPlaceholder(std::string_view name) const;

    std::string m_condition;
    std::vector<Variant> m_paramValues;                      // For positional parameters
    std::unordered_map<std::string, Variant> m_namedParams;  // For named parameters
    ParameterStyle m_paramStyle = ParameterStyle::QuestionMark;
    mutable int m_paramCounter = 0;

    // Static default parameter style
    static inline ParameterStyle s_defaultParamStyle = ParameterStyle::QuestionMark;
};

} // namespace database

#endif // DATABASE_SQL_CONDITION_H
