load("@rules_cc//cc:defs.bzl", "cc_library")

licenses(["notice"])

COPTS = [
    "-pedantic",
    "-pedantic-errors",
    "-std=c++17",
    "-Wall",
    "-Wconversion",
    "-Wextra",
    "-Wshadow",
    #    "-Wshorten-64-to-32",
    "-Wfloat-equal",
    "-fstrict-aliasing",
    ## assert() are used a lot in tests upstream, which may be optimised out leading to
    ## unused-variable warning.
    "-Wno-unused-variable",
    "-Werror=old-style-cast",
]

MSVC_COPTS = [
    "/std:c++17",
]

config_setting(
    name = "windows",
    constraint_values = ["@platforms//os:windows"],
    visibility = [":__subpackages__"],
)

config_setting(
    name = "perfcounters",
    define_values = {
        "pfm": "1",
    },
    visibility = [":__subpackages__"],
)

cc_library(
    name = "benchmark",
    srcs = glob(
        [
            "src/*.cc",
            "src/*.h",
        ],
        exclude = ["src/benchmark_main.cc"],
    ),
    hdrs = [
        "include/benchmark/benchmark.h",
        "include/benchmark/export.h",
    ],
    copts = select({
        ":windows": MSVC_COPTS,
        "//conditions:default": COPTS,
    }),
    defines = [
        "BENCHMARK_STATIC_DEFINE",
        "BENCHMARK_VERSION=\\\"" + (module_version() if module_version() != None else "") + "\\\"",
    ] + select({
        ":perfcounters": ["HAVE_LIBPFM"],
        "//conditions:default": [],
    }),
    includes = ["include"],
    linkopts = select({
        ":windows": ["-DEFAULTLIB:shlwapi.lib"],
        "//conditions:default": ["-pthread"],
    }),
    # Only static linking is allowed; no .so will be produced.
    # Using `defines` (i.e. not `local_defines`) means that no
    # dependent rules need to bother about defining the macro.
    linkstatic = True,
    local_defines = [
        # Turn on Large-file Support
        "_FILE_OFFSET_BITS=64",
        "_LARGEFILE64_SOURCE",
        "_LARGEFILE_SOURCE",
    ],
    visibility = ["//visibility:public"],
    deps = select({
        ":perfcounters": ["@libpfm"],
        "//conditions:default": [],
    }),
)

cc_library(
    name = "benchmark_main",
    srcs = ["src/benchmark_main.cc"],
    hdrs = [
        "include/benchmark/benchmark.h",
        "include/benchmark/export.h",
    ],
    includes = ["include"],
    visibility = ["//visibility:public"],
    deps = [":benchmark"],
)

cc_library(
    name = "benchmark_internal_headers",
    hdrs = glob(["src/*.h"]),
    visibility = ["//test:__pkg__"],
)
