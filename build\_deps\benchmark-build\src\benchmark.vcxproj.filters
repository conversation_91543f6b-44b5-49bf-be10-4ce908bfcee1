﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_api_internal.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_name.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_register.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_runner.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\check.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\colorprint.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\commandlineflags.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\complexity.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\console_reporter.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\counter.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\csv_reporter.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\json_reporter.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\perf_counters.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\reporter.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\statistics.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\string_util.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\sysinfo.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\timers.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include\benchmark\benchmark.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include\benchmark\export.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\arraysize.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_api_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_register.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_runner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\check.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\colorprint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\commandlineflags.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\complexity.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\counter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\cycleclock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\internal_macros.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\mutex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\perf_counters.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\re.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\statistics.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\string_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\thread_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\thread_timer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\timers.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{4288055C-9503-3AAF-AD4B-7981EFB69A46}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{6499DCDF-FE9A-3BEC-A12C-E6B2EDA78214}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
