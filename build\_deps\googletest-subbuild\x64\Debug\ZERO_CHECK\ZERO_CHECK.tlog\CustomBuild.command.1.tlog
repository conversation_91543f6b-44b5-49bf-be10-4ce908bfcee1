^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\DCABC46AD6D2265B9F55A16B45B7E308\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild" "-BE:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
