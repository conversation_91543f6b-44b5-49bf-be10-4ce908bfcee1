# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file Copyright.txt or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION 3.5)

file(MAKE_DIRECTORY
  "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-src"
  "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-build"
  "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix"
  "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/tmp"
  "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp"
  "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src"
  "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp"
)

set(configSubDirs Debug)
foreach(subDir IN LISTS configSubDirs)
    file(MAKE_DIRECTORY "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/${subDir}")
endforeach()
if(cfgdir)
  file(MAKE_DIRECTORY "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp${cfgdir}") # cfgdir has leading slash
endif()
