﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\45bf86dc109fdaadae6c3e373ea767b3\benchmark-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\45bf86dc109fdaadae6c3e373ea767b3\benchmark-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\45bf86dc109fdaadae6c3e373ea767b3\benchmark-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\45bf86dc109fdaadae6c3e373ea767b3\benchmark-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\45bf86dc109fdaadae6c3e373ea767b3\benchmark-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\45bf86dc109fdaadae6c3e373ea767b3\benchmark-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\45bf86dc109fdaadae6c3e373ea767b3\benchmark-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\45bf86dc109fdaadae6c3e373ea767b3\benchmark-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\f2a2ffd668a04d71e39fa8fad3adf21c\benchmark-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\8bd7b8936d85ef22dfd1b306c53425eb\benchmark-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-subbuild\CMakeFiles\benchmark-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{7E78BBD9-11B1-3581-9D37-2CA9068D0781}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
