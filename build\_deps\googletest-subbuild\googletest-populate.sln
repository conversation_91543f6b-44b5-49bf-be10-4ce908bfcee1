﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{38E518D7-7BF1-35C2-970E-6C0DDB159B1E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{5DD85281-D6EC-3E96-8195-71D67E442F74}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "googletest-populate", "ExternalProjectTargets\googletest-populate", "{2088E012-5A27-328A-919B-D4E7A085C47D}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{7CFC2CBF-4B4B-3E1C-9B8F-A47518BAFDB0}"
	ProjectSection(ProjectDependencies) = postProject
		{0DB52244-9418-37A2-B111-4448FE1D88D6} = {0DB52244-9418-37A2-B111-4448FE1D88D6}
		{FB7C6101-FB24-326B-973A-F682413590C1} = {FB7C6101-FB24-326B-973A-F682413590C1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{0DB52244-9418-37A2-B111-4448FE1D88D6}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "googletest-populate", "googletest-populate.vcxproj", "{FB7C6101-FB24-326B-973A-F682413590C1}"
	ProjectSection(ProjectDependencies) = postProject
		{0DB52244-9418-37A2-B111-4448FE1D88D6} = {0DB52244-9418-37A2-B111-4448FE1D88D6}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7CFC2CBF-4B4B-3E1C-9B8F-A47518BAFDB0}.Debug|x64.ActiveCfg = Debug|x64
		{0DB52244-9418-37A2-B111-4448FE1D88D6}.Debug|x64.ActiveCfg = Debug|x64
		{0DB52244-9418-37A2-B111-4448FE1D88D6}.Debug|x64.Build.0 = Debug|x64
		{FB7C6101-FB24-326B-973A-F682413590C1}.Debug|x64.ActiveCfg = Debug|x64
		{FB7C6101-FB24-326B-973A-F682413590C1}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7CFC2CBF-4B4B-3E1C-9B8F-A47518BAFDB0} = {38E518D7-7BF1-35C2-970E-6C0DDB159B1E}
		{0DB52244-9418-37A2-B111-4448FE1D88D6} = {38E518D7-7BF1-35C2-970E-6C0DDB159B1E}
		{2088E012-5A27-328A-919B-D4E7A085C47D} = {5DD85281-D6EC-3E96-8195-71D67E442F74}
		{FB7C6101-FB24-326B-973A-F682413590C1} = {2088E012-5A27-328A-919B-D4E7A085C47D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BA56AF40-B73A-3486-89D4-3797F8CE6A99}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
