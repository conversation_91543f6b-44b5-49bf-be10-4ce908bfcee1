﻿#ifndef DATABASE_SQL_ENUMS_H
#define DATABASE_SQL_ENUMS_H

#include <string>
#include <string_view>
#include <array>
#include <algorithm>

namespace database {

/**
 * @brief Error codes for database operations
 *
 * Error codes are organized into categories by ranges:
 * - General errors:     0-99
 * - Connection errors:  100-199
 * - Execution errors:   200-299
 * - Transaction errors: 300-399
 * - Constraint errors:  400-499
 * - Resource errors:    500-599
 */
enum class ErrorCode : int16_t {
    // General errors (0-99)
    Unknown = 0,                  ///< Unknown or unspecified error
    InvalidArgument = 1,          ///< Invalid argument or parameter
    NotImplemented = 2,           ///< Feature or operation not implemented
    OperationCancelled = 3,       ///< Operation was cancelled
    InternalError = 4,            ///< Internal error in the database system

    // Connection errors (100-199)
    ConnectionFailed = 100,       ///< Failed to establish connection
    ConnectionTimeout = 101,      ///< Connection timed out
    ConnectionClosed = 102,       ///< Connection is closed
    AuthenticationFailed = 103,   ///< Authentication failed
    NetworkError = 104,           ///< Network-related error

    // Execution errors (200-299)
    ExecutionFailed = 200,        ///< General execution failure
    StatementInvalid = 201,       ///< Statement is invalid
    ParameterBindingFailed = 202, ///< Failed to bind parameters
    ResultSetClosed = 203,        ///< Result set is closed
    QueryTimeout = 204,           ///< Query execution timed out
    SyntaxError = 205,            ///< SQL syntax error

    // Transaction errors (300-399)
    TransactionFailed = 300,      ///< General transaction failure
    TransactionAlreadyActive = 301, ///< Transaction is already active
    TransactionNotActive = 302,   ///< No active transaction
    TransactionRollbackOnly = 303, ///< Transaction is marked for rollback only
    DeadlockDetected = 304,       ///< Deadlock was detected

    // Constraint errors (400-499)
    ConstraintViolation = 400,    ///< General constraint violation
    UniqueConstraintViolation = 401, ///< Unique constraint violation
    ForeignKeyConstraintViolation = 402, ///< Foreign key constraint violation
    CheckConstraintViolation = 403, ///< Check constraint violation
    NotNullConstraintViolation = 404, ///< Not null constraint violation

    // Resource errors (500-599)
    ResourceError = 500,          ///< General resource error
    DatabaseLocked = 501,         ///< Database is locked
    ResultInvalid = 502,          ///< Result is invalid
    OutOfMemory = 503,            ///< Out of memory
    DiskFull = 504,               ///< Disk is full
    TooManyConnections = 505      ///< Too many connections
};

/**
 * @brief Transaction operations
 */
enum class TransactionOperation : uint8_t {
    Unknown,
    Begin,
    Commit,
    Rollback,
    SavePoint,
    RollbackToSavePoint,
    ReleaseSavePoint
};

/**
 * @brief Enumeration of SQL comparison operators
 */
enum class SqlConditionOperator : uint8_t {
    Equal,              // =
    NotEqual,           // <>
    LessThan,           // <
    LessEqual,          // <=
    GreaterThan,        // >
    GreaterEqual,       // >=
    Like,               // LIKE
    NotLike,            // NOT LIKE
    In,                 // IN
    NotIn,              // NOT IN
    Between,            // BETWEEN
    IsNull,             // IS NULL
    IsNotNull           // IS NOT NULL
};

/**
 * @brief Enumeration of SQL logical operators
 */
enum class SqlLogicalOperator : uint8_t {
    And,                // AND
    Or,                 // OR
    Not                 // NOT
};

/**
 * @brief Enumeration of SQL sort orders
 */
enum class SqlSortOrder : uint8_t {
    Ascending,          // ASC
    Descending          // DESC
};

/**
 * @brief Enumeration of SQL join types
 */
enum class SqlJoinType : uint8_t {
    Inner,              // INNER JOIN
    Left,               // LEFT JOIN
    Right,              // RIGHT JOIN
    Full,               // FULL JOIN
    Cross               // CROSS JOIN
};

/**
 * @brief Convert a SqlConditionOperator to its SQL string representation
 * @param op The operator to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlOperatorToString(SqlConditionOperator op) noexcept {
    switch (op) {
        case SqlConditionOperator::Equal:        return "=";
        case SqlConditionOperator::NotEqual:     return "<>";
        case SqlConditionOperator::LessThan:     return "<";
        case SqlConditionOperator::LessEqual:    return "<=";
        case SqlConditionOperator::GreaterThan:  return ">";
        case SqlConditionOperator::GreaterEqual: return ">=";
        case SqlConditionOperator::Like:         return "LIKE";
        case SqlConditionOperator::NotLike:      return "NOT LIKE";
        case SqlConditionOperator::In:           return "IN";
        case SqlConditionOperator::NotIn:        return "NOT IN";
        case SqlConditionOperator::Between:      return "BETWEEN";
        case SqlConditionOperator::IsNull:       return "IS NULL";
        case SqlConditionOperator::IsNotNull:    return "IS NOT NULL";
        default:                                 return "=";
    }
}

/**
 * @brief Convert a SqlSortOrder to its SQL string representation
 * @param order The sort order to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlSortOrderToString(SqlSortOrder order) noexcept {
    switch (order) {
        case SqlSortOrder::Ascending:  return "ASC";
        case SqlSortOrder::Descending: return "DESC";
        default:                       return "ASC";
    }
}

/**
 * @brief Convert a SqlJoinType to its SQL string representation
 * @param joinType The join type to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlJoinTypeToString(SqlJoinType joinType) noexcept {
    switch (joinType) {
        case SqlJoinType::Inner: return "INNER JOIN";
        case SqlJoinType::Left:  return "LEFT JOIN";
        case SqlJoinType::Right: return "RIGHT JOIN";
        case SqlJoinType::Full:  return "FULL JOIN";
        case SqlJoinType::Cross: return "CROSS JOIN";
        default:                 return "INNER JOIN";
    }
}

/**
 * @brief Convert a SqlLogicalOperator to its SQL string representation
 * @param op The logical operator to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlLogicalOperatorToString(SqlLogicalOperator op) noexcept {
    switch (op) {
        case SqlLogicalOperator::And: return "AND";
        case SqlLogicalOperator::Or:  return "OR";
        case SqlLogicalOperator::Not: return "NOT";
        default:                      return "AND";
    }
}

/**
 * @brief Convert a string to its SqlConditionOperator enum value
 * @param opStr The string representation of the operator
 * @return The SqlConditionOperator enum value, or SqlConditionOperator::Equal if not recognized
 */
[[nodiscard]] inline SqlConditionOperator stringToSqlOperator(std::string_view opStr) noexcept {
    if (opStr == "=" || opStr == "==") return SqlConditionOperator::Equal;
    if (opStr == "<>" || opStr == "!=") return SqlConditionOperator::NotEqual;
    if (opStr == "<") return SqlConditionOperator::LessThan;
    if (opStr == "<=") return SqlConditionOperator::LessEqual;
    if (opStr == ">") return SqlConditionOperator::GreaterThan;
    if (opStr == ">=") return SqlConditionOperator::GreaterEqual;
    if (opStr == "LIKE" || opStr == "like") return SqlConditionOperator::Like;
    if (opStr == "NOT LIKE" || opStr == "not like") return SqlConditionOperator::NotLike;
    if (opStr == "IN" || opStr == "in") return SqlConditionOperator::In;
    if (opStr == "NOT IN" || opStr == "not in") return SqlConditionOperator::NotIn;
    if (opStr == "BETWEEN" || opStr == "between") return SqlConditionOperator::Between;
    if (opStr == "IS NULL" || opStr == "is null") return SqlConditionOperator::IsNull;
    if (opStr == "IS NOT NULL" || opStr == "is not null") return SqlConditionOperator::IsNotNull;

    // Default to Equal if not recognized
    return SqlConditionOperator::Equal;
}

} // namespace database

#endif // DATABASE_SQL_ENUMS_H
