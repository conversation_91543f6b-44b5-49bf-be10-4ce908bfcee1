^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\CMAKEFILES\E9FD24F9FA573F46AE9BA11ABB8E1FC0\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5" "-BE:/Projects/Code/AI Agent/database_v11.5/build" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "E:/Projects/Code/AI Agent/database_v11.5/build/database_test.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
