
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
      鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:20:34銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
        鎴栨壒澶勭悊鏂囦欢銆?
        鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:06.08
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/3.28.1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9xmqj4"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9xmqj4"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9xmqj4'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_2a19c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:20:41銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9xmqj4\\cmTC_2a19c.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_2a19c.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9xmqj4\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_2a19c.dir\\Debug\\cmTC_2a19c.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_2a19c.dir\\Debug\\cmTC_2a19c.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_2a19c.dir\\Debug\\cmTC_2a19c.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_2a19c.dir\\Debug\\\\" /Fd"cmTC_2a19c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_2a19c.dir\\Debug\\\\" /Fd"cmTC_2a19c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9xmqj4\\Debug\\cmTC_2a19c.exe" /INCREMENTAL /ILK:"cmTC_2a19c.dir\\Debug\\cmTC_2a19c.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9xmqj4/Debug/cmTC_2a19c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9xmqj4/Debug/cmTC_2a19c.lib" /MACHINE:X64  /machine:x64 cmTC_2a19c.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_2a19c.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9xmqj4\\Debug\\cmTC_2a19c.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9xmqj4\\Debug\\cmTC_2a19c.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_2a19c.dir\\Debug\\cmTC_2a19c.tlog\\cmTC_2a19c.write.1u.tlog" "cmTC_2a19c.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
          鎴栨壒澶勭悊鏂囦欢銆?
          鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9xmqj4\\Debug\\cmTC_2a19c.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_2a19c.dir\\Debug\\cmTC_2a19c.tlog\\cmTC_2a19c.write.1u.tlog" "cmTC_2a19c.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9xmqj4\\Debug\\cmTC_2a19c.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_2a19c.dir\\Debug\\cmTC_2a19c.tlog\\cmTC_2a19c.write.1u.tlog" "cmTC_2a19c.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_2a19c.dir\\Debug\\cmTC_2a19c.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_2a19c.dir\\Debug\\cmTC_2a19c.tlog\\cmTC_2a19c.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9xmqj4\\cmTC_2a19c.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.93
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "build/_deps/googletest-src/CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
      鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:02銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdC\\CompilerIdC.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
        鎴栨壒澶勭悊鏂囦欢銆?
        鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdC\\CompilerIdC.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdC\\CompilerIdC.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\3.28.1\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:01.02
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/3.28.1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "build/_deps/googletest-src/CMakeLists.txt:6 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-y112fk"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-y112fk"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-y112fk'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_b288f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:04銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y112fk\\cmTC_b288f.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b288f.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y112fk\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b288f.dir\\Debug\\cmTC_b288f.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_b288f.dir\\Debug\\cmTC_b288f.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_b288f.dir\\Debug\\cmTC_b288f.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_b288f.dir\\Debug\\\\" /Fd"cmTC_b288f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_b288f.dir\\Debug\\\\" /Fd"cmTC_b288f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y112fk\\Debug\\cmTC_b288f.exe" /INCREMENTAL /ILK:"cmTC_b288f.dir\\Debug\\cmTC_b288f.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-y112fk/Debug/cmTC_b288f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-y112fk/Debug/cmTC_b288f.lib" /MACHINE:X64  /machine:x64 cmTC_b288f.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_b288f.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y112fk\\Debug\\cmTC_b288f.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y112fk\\Debug\\cmTC_b288f.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_b288f.dir\\Debug\\cmTC_b288f.tlog\\cmTC_b288f.write.1u.tlog" "cmTC_b288f.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
          鎴栨壒澶勭悊鏂囦欢銆?
          鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y112fk\\Debug\\cmTC_b288f.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_b288f.dir\\Debug\\cmTC_b288f.tlog\\cmTC_b288f.write.1u.tlog" "cmTC_b288f.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y112fk\\Debug\\cmTC_b288f.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_b288f.dir\\Debug\\cmTC_b288f.tlog\\cmTC_b288f.write.1u.tlog" "cmTC_b288f.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_b288f.dir\\Debug\\cmTC_b288f.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_b288f.dir\\Debug\\cmTC_b288f.tlog\\cmTC_b288f.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y112fk\\cmTC_b288f.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.94
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "build/_deps/googletest-src/googletest/cmake/internal_utils.cmake:66 (find_package)"
      - "build/_deps/googletest-src/googletest/CMakeLists.txt:83 (config_compiler_and_linker)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-3cclcs"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-3cclcs"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-3cclcs'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_96079.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:05銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\cmTC_96079.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_96079.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_96079.dir\\Debug\\cmTC_96079.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_96079.dir\\Debug\\cmTC_96079.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_96079.dir\\Debug\\cmTC_96079.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_96079.dir\\Debug\\\\" /Fd"cmTC_96079.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_96079.dir\\Debug\\\\" /Fd"cmTC_96079.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\src.c"
          src.c
        E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\cmTC_96079.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\cmTC_96079.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\cmTC_96079.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3cclcs\\cmTC_96079.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.36
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "build/_deps/googletest-src/googletest/cmake/internal_utils.cmake:66 (find_package)"
      - "build/_deps/googletest-src/googletest/CMakeLists.txt:83 (config_compiler_and_linker)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9f2ixg"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9f2ixg"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9f2ixg'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_9f8f2.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:06銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\cmTC_9f8f2.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_9f8f2.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_9f8f2.dir\\Debug\\cmTC_9f8f2.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_9f8f2.dir\\Debug\\cmTC_9f8f2.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_9f8f2.dir\\Debug\\cmTC_9f8f2.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_9f8f2.dir\\Debug\\\\" /Fd"cmTC_9f8f2.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_9f8f2.dir\\Debug\\\\" /Fd"cmTC_9f8f2.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\Debug\\cmTC_9f8f2.exe" /INCREMENTAL /ILK:"cmTC_9f8f2.dir\\Debug\\cmTC_9f8f2.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9f2ixg/Debug/cmTC_9f8f2.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-9f2ixg/Debug/cmTC_9f8f2.lib" /MACHINE:X64  /machine:x64 cmTC_9f8f2.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\cmTC_9f8f2.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\cmTC_9f8f2.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\cmTC_9f8f2.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9f2ixg\\cmTC_9f8f2.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.35
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "build/_deps/googletest-src/googletest/cmake/internal_utils.cmake:66 (find_package)"
      - "build/_deps/googletest-src/googletest/CMakeLists.txt:83 (config_compiler_and_linker)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-pgeqtx"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-pgeqtx"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-pgeqtx'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_37447.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:06銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\cmTC_37447.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_37447.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_37447.dir\\Debug\\cmTC_37447.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_37447.dir\\Debug\\cmTC_37447.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_37447.dir\\Debug\\cmTC_37447.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_37447.dir\\Debug\\\\" /Fd"cmTC_37447.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_37447.dir\\Debug\\\\" /Fd"cmTC_37447.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\Debug\\cmTC_37447.exe" /INCREMENTAL /ILK:"cmTC_37447.dir\\Debug\\cmTC_37447.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-pgeqtx/Debug/cmTC_37447.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-pgeqtx/Debug/cmTC_37447.lib" /MACHINE:X64  /machine:x64 cmTC_37447.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\cmTC_37447.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\cmTC_37447.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\cmTC_37447.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pgeqtx\\cmTC_37447.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.38
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "build/_deps/benchmark-src/CMakeLists.txt:135 (check_library_exists)"
    checks:
      - "Looking for shm_open in rt"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-60086t"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-60086t"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake/Modules;E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake"
    buildResult:
      variable: "HAVE_LIB_RT"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-60086t'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_4c8e6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:32銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\cmTC_4c8e6.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_4c8e6.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_4c8e6.dir\\Debug\\cmTC_4c8e6.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_4c8e6.dir\\Debug\\cmTC_4c8e6.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_4c8e6.dir\\Debug\\cmTC_4c8e6.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=shm_open /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_4c8e6.dir\\Debug\\\\" /Fd"cmTC_4c8e6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=shm_open /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_4c8e6.dir\\Debug\\\\" /Fd"cmTC_4c8e6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\Debug\\cmTC_4c8e6.exe" /INCREMENTAL /ILK:"cmTC_4c8e6.dir\\Debug\\cmTC_4c8e6.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" rt.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-60086t/Debug/cmTC_4c8e6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-60086t/Debug/cmTC_4c8e6.lib" /MACHINE:X64  /machine:x64 cmTC_4c8e6.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥渞t.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\cmTC_4c8e6.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\cmTC_4c8e6.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\cmTC_4c8e6.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥渞t.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-60086t\\cmTC_4c8e6.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.41
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake:34 (cmake_check_compiler_flag)"
      - "build/_deps/benchmark-src/cmake/AddCXXCompilerFlag.cmake:34 (check_cxx_compiler_flag)"
      - "build/_deps/benchmark-src/CMakeLists.txt:159 (add_cxx_compiler_flag)"
    checks:
      - "Performing Test HAVE_CXX_FLAG_WX"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-c7ad8m"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-c7ad8m"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS  /GR /EHsc /W4 /MP"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake/Modules;E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake"
    buildResult:
      variable: "HAVE_CXX_FLAG_WX"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-c7ad8m'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_b8ce0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:32銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\cmTC_b8ce0.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b8ce0.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b8ce0.dir\\Debug\\cmTC_b8ce0.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_b8ce0.dir\\Debug\\cmTC_b8ce0.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_b8ce0.dir\\Debug\\cmTC_b8ce0.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_CXX_FLAG_WX /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_b8ce0.dir\\Debug\\\\" /Fd"cmTC_b8ce0.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_CXX_FLAG_WX /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_b8ce0.dir\\Debug\\\\" /Fd"cmTC_b8ce0.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\Debug\\cmTC_b8ce0.exe" /INCREMENTAL /ILK:"cmTC_b8ce0.dir\\Debug\\cmTC_b8ce0.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-c7ad8m/Debug/cmTC_b8ce0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeScratch/TryCompile-c7ad8m/Debug/cmTC_b8ce0.lib" /MACHINE:X64  /machine:x64 cmTC_b8ce0.dir\\Debug\\src.obj
          cmTC_b8ce0.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\Debug\\cmTC_b8ce0.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\Debug\\cmTC_b8ce0.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_b8ce0.dir\\Debug\\cmTC_b8ce0.tlog\\cmTC_b8ce0.write.1u.tlog" "cmTC_b8ce0.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
          鎴栨壒澶勭悊鏂囦欢銆?
          鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\Debug\\cmTC_b8ce0.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_b8ce0.dir\\Debug\\cmTC_b8ce0.tlog\\cmTC_b8ce0.write.1u.tlog" "cmTC_b8ce0.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\Debug\\cmTC_b8ce0.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_b8ce0.dir\\Debug\\cmTC_b8ce0.tlog\\cmTC_b8ce0.write.1u.tlog" "cmTC_b8ce0.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_b8ce0.dir\\Debug\\cmTC_b8ce0.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_b8ce0.dir\\Debug\\cmTC_b8ce0.tlog\\cmTC_b8ce0.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeScratch\\TryCompile-c7ad8m\\cmTC_b8ce0.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.00
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "build/_deps/benchmark-src/cmake/CXXFeatureCheck.cmake:57 (try_run)"
      - "build/_deps/benchmark-src/CMakeLists.txt:311 (cxx_feature_check)"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS  /GR /EHsc /W4 /MP  -WX"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake/Modules;E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake"
    buildResult:
      variable: "COMPILE_HAVE_STD_REGEX"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c499e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:34銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_c499e.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c499e.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c499e.dir\\Debug\\cmTC_c499e.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_c499e.dir\\Debug\\cmTC_c499e.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_c499e.dir\\Debug\\cmTC_c499e.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_c499e.dir\\Debug\\\\" /Fd"cmTC_c499e.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\std_regex.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_c499e.dir\\Debug\\\\" /Fd"cmTC_c499e.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\std_regex.cpp"
          std_regex.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_c499e.exe" /INCREMENTAL /ILK:"cmTC_c499e.dir\\Debug\\cmTC_c499e.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp/Debug/cmTC_c499e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp/Debug/cmTC_c499e.lib" /MACHINE:X64  /machine:x64 cmTC_c499e.dir\\Debug\\std_regex.obj
          cmTC_c499e.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_c499e.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_c499e.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_c499e.dir\\Debug\\cmTC_c499e.tlog\\cmTC_c499e.write.1u.tlog" "cmTC_c499e.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
          鎴栨壒澶勭悊鏂囦欢銆?
          鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_c499e.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_c499e.dir\\Debug\\cmTC_c499e.tlog\\cmTC_c499e.write.1u.tlog" "cmTC_c499e.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_c499e.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_c499e.dir\\Debug\\cmTC_c499e.tlog\\cmTC_c499e.write.1u.tlog" "cmTC_c499e.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_c499e.dir\\Debug\\cmTC_c499e.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_c499e.dir\\Debug\\cmTC_c499e.tlog\\cmTC_c499e.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_c499e.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.65
        
      exitCode: 0
    runResult:
      variable: "RUN_HAVE_STD_REGEX"
      cached: true
      stdout: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "build/_deps/benchmark-src/cmake/CXXFeatureCheck.cmake:57 (try_run)"
      - "build/_deps/benchmark-src/CMakeLists.txt:312 (cxx_feature_check)"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS  /GR /EHsc /W4 /MP  -WX"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake/Modules;E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake"
    buildResult:
      variable: "COMPILE_HAVE_GNU_POSIX_REGEX"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_fb13d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:36銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_fb13d.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_fb13d.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_fb13d.dir\\Debug\\cmTC_fb13d.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_fb13d.dir\\Debug\\cmTC_fb13d.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_fb13d.dir\\Debug\\cmTC_fb13d.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_fb13d.dir\\Debug\\\\" /Fd"cmTC_fb13d.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\gnu_posix_regex.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_fb13d.dir\\Debug\\\\" /Fd"cmTC_fb13d.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\gnu_posix_regex.cpp"
          gnu_posix_regex.cpp
        E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\gnu_posix_regex.cpp(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥済nuregex.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_fb13d.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_fb13d.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_fb13d.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\gnu_posix_regex.cpp(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥済nuregex.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_fb13d.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.30
        
      exitCode: 1
    runResult:
      variable: "RUN_HAVE_GNU_POSIX_REGEX"
      cached: true
  -
    kind: "try_run-v1"
    backtrace:
      - "build/_deps/benchmark-src/cmake/CXXFeatureCheck.cmake:57 (try_run)"
      - "build/_deps/benchmark-src/CMakeLists.txt:313 (cxx_feature_check)"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS  /GR /EHsc /W4 /MP  -WX"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake/Modules;E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake"
    buildResult:
      variable: "COMPILE_HAVE_POSIX_REGEX"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c34a6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:36銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_c34a6.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c34a6.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c34a6.dir\\Debug\\cmTC_c34a6.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_c34a6.dir\\Debug\\cmTC_c34a6.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_c34a6.dir\\Debug\\cmTC_c34a6.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_c34a6.dir\\Debug\\\\" /Fd"cmTC_c34a6.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\posix_regex.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_c34a6.dir\\Debug\\\\" /Fd"cmTC_c34a6.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\posix_regex.cpp"
          posix_regex.cpp
        E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\posix_regex.cpp(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渞egex.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_c34a6.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_c34a6.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_c34a6.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\posix_regex.cpp(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渞egex.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_c34a6.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.32
        
      exitCode: 1
    runResult:
      variable: "RUN_HAVE_POSIX_REGEX"
      cached: true
  -
    kind: "try_run-v1"
    backtrace:
      - "build/_deps/benchmark-src/cmake/CXXFeatureCheck.cmake:57 (try_run)"
      - "build/_deps/benchmark-src/CMakeLists.txt:322 (cxx_feature_check)"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS  /GR /EHsc /W4 /MP  -WX"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake/Modules;E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake"
    buildResult:
      variable: "COMPILE_HAVE_STEADY_CLOCK"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_48ca0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:37銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_48ca0.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_48ca0.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_48ca0.dir\\Debug\\cmTC_48ca0.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_48ca0.dir\\Debug\\cmTC_48ca0.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_48ca0.dir\\Debug\\cmTC_48ca0.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_48ca0.dir\\Debug\\\\" /Fd"cmTC_48ca0.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\steady_clock.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_48ca0.dir\\Debug\\\\" /Fd"cmTC_48ca0.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\steady_clock.cpp"
          steady_clock.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_48ca0.exe" /INCREMENTAL /ILK:"cmTC_48ca0.dir\\Debug\\cmTC_48ca0.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp/Debug/cmTC_48ca0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp/Debug/cmTC_48ca0.lib" /MACHINE:X64  /machine:x64 cmTC_48ca0.dir\\Debug\\steady_clock.obj
          cmTC_48ca0.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_48ca0.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_48ca0.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_48ca0.dir\\Debug\\cmTC_48ca0.tlog\\cmTC_48ca0.write.1u.tlog" "cmTC_48ca0.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
          鎴栨壒澶勭悊鏂囦欢銆?
          鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_48ca0.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_48ca0.dir\\Debug\\cmTC_48ca0.tlog\\cmTC_48ca0.write.1u.tlog" "cmTC_48ca0.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_48ca0.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_48ca0.dir\\Debug\\cmTC_48ca0.tlog\\cmTC_48ca0.write.1u.tlog" "cmTC_48ca0.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_48ca0.dir\\Debug\\cmTC_48ca0.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_48ca0.dir\\Debug\\cmTC_48ca0.tlog\\cmTC_48ca0.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_48ca0.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.15
        
      exitCode: 0
    runResult:
      variable: "RUN_HAVE_STEADY_CLOCK"
      cached: true
      stdout: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "build/_deps/benchmark-src/cmake/CXXFeatureCheck.cmake:57 (try_run)"
      - "build/_deps/benchmark-src/CMakeLists.txt:326 (cxx_feature_check)"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
      binary: "E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS  /GR /EHsc /W4 /MP  -WX"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake/Modules;E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/cmake"
    buildResult:
      variable: "COMPILE_HAVE_PTHREAD_AFFINITY"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v11.5/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_a90d5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/26 15:21:38銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_a90d5.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_a90d5.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_a90d5.dir\\Debug\\cmTC_a90d5.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_a90d5.dir\\Debug\\cmTC_a90d5.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_a90d5.dir\\Debug\\cmTC_a90d5.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_a90d5.dir\\Debug\\\\" /Fd"cmTC_a90d5.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\pthread_affinity.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W4 /WX /diagnostics:column /MP /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_a90d5.dir\\Debug\\\\" /Fd"cmTC_a90d5.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\pthread_affinity.cpp"
          pthread_affinity.cpp
        E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\pthread_affinity.cpp(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_a90d5.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_a90d5.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_a90d5.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\_deps\\benchmark-src\\cmake\\pthread_affinity.cpp(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v11.5\\build\\CMakeFiles\\CMakeTmp\\cmTC_a90d5.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.34
        
      exitCode: 1
    runResult:
      variable: "RUN_HAVE_PTHREAD_AFFINITY"
      cached: true
...
