name: Build and upload Python wheels

on:
  workflow_dispatch:
  release:
    types:
      - published

env:
  CMAKE_GENERATOR: Ninja

jobs:
  build_sdist:
    name: Build source distribution
    runs-on: ubuntu-latest
    steps:
      - name: Check out repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
      - run: python -m pip install build
      - name: Build sdist
        run: python -m build --sdist
      - uses: actions/upload-artifact@v4
        with:
          name: dist-sdist
          path: dist/*.tar.gz

  build_wheels:
    name: Build Google Benchmark wheels on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, ubuntu-24.04-arm, macos-13, macos-14, windows-latest]
    steps:
      - name: Check out Google Benchmark
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-python@v5
        name: Install Python 3.12
        with:
          python-version: "3.12"
      - run: pip install --upgrade pip uv

      - name: Build wheels on ${{ matrix.os }} using cibuildwheel
        uses: pypa/cibuildwheel@v2.23.2
        env:
          CIBW_BUILD: "cp310-* cp311-* cp312-*"
          CIBW_BUILD_FRONTEND: "build[uv]"
          CIBW_SKIP: "*-musllinux_*"
          CIBW_ARCHS: auto64
          CIBW_BEFORE_ALL_LINUX: bash .github/install_bazel.sh
          # Grab the rootless Bazel installation inside the container.
          CIBW_ENVIRONMENT_LINUX: PATH=$PATH:$HOME/bin
          CIBW_TEST_COMMAND: python {project}/bindings/python/google_benchmark/example.py
          # unused by Bazel, but needed explicitly by delocate on MacOS.
          MACOSX_DEPLOYMENT_TARGET: "10.14"

      - name: Upload Google Benchmark ${{ matrix.os }} wheels
        uses: actions/upload-artifact@v4
        with:
          name: dist-${{ matrix.os }}
          path: wheelhouse/*.whl

  pypi_upload:
    name: Publish google-benchmark wheels to PyPI
    needs: [build_sdist, build_wheels]
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
      - uses: actions/download-artifact@v4
        with:
          path: dist
          pattern: dist-*
          merge-multiple: true
      - uses: pypa/gh-action-pypi-publish@release/v1
