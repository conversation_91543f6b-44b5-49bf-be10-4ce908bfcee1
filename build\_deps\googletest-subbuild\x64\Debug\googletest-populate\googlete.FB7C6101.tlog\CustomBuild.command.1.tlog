^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\ED1BDD72BA717E216F09DF065F64E0F7\GOOGLETEST-POPULATE-MKDIR.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/tmp/googletest-populate-mkdirs.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/Debug/googletest-populate-mkdir"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\ED1BDD72BA717E216F09DF065F64E0F7\GOOGLETEST-POPULATE-DOWNLOAD.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -P "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/tmp/googletest-populate-gitclone.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/Debug/googletest-populate-download"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\ED1BDD72BA717E216F09DF065F64E0F7\GOOGLETEST-POPULATE-UPDATE.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\googletest-src"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -Dcan_fetch=YES -P "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/tmp/googletest-populate-gitupdate.cmake"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\ED1BDD72BA717E216F09DF065F64E0F7\GOOGLETEST-POPULATE-PATCH.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/Debug/googletest-populate-patch"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\ED1BDD72BA717E216F09DF065F64E0F7\GOOGLETEST-POPULATE-CONFIGURE.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\googletest-build"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/Debug/googletest-populate-configure"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\ED1BDD72BA717E216F09DF065F64E0F7\GOOGLETEST-POPULATE-BUILD.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\googletest-build"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/Debug/googletest-populate-build"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\ED1BDD72BA717E216F09DF065F64E0F7\GOOGLETEST-POPULATE-INSTALL.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\googletest-build"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/Debug/googletest-populate-install"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\ED1BDD72BA717E216F09DF065F64E0F7\GOOGLETEST-POPULATE-TEST.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\googletest-build"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/Debug/googletest-populate-test"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\C4666E83BBA5E6BE992A550A5A32092F\GOOGLETEST-POPULATE-COMPLETE.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/CMakeFiles/Debug"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/CMakeFiles/Debug/googletest-populate-complete"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/googletest-populate-prefix/src/googletest-populate-stamp/Debug/googletest-populate-done"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\DCABC46AD6D2265B9F55A16B45B7E308\GOOGLETEST-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild" "-BE:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild" --check-stamp-file "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/googletest-subbuild/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
