﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{EF929AE0-7F2A-3B59-88FB-44CA75684298}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{FF6D2FB8-126B-3D1D-8E46-CB7F5CA18917}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "benchmark-populate", "ExternalProjectTargets\benchmark-populate", "{47B3954E-FCA8-3E70-8FC1-513443BE52A3}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{51F4DBDB-4921-358C-A2C7-89C81B0DB47D}"
	ProjectSection(ProjectDependencies) = postProject
		{0FE66756-3003-3578-B36C-1AEEE16C4526} = {0FE66756-3003-3578-B36C-1AEEE16C4526}
		{4DF95153-C904-3ACE-95EB-E87D953A279D} = {4DF95153-C904-3ACE-95EB-E87D953A279D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{0FE66756-3003-3578-B36C-1AEEE16C4526}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "benchmark-populate", "benchmark-populate.vcxproj", "{4DF95153-C904-3ACE-95EB-E87D953A279D}"
	ProjectSection(ProjectDependencies) = postProject
		{0FE66756-3003-3578-B36C-1AEEE16C4526} = {0FE66756-3003-3578-B36C-1AEEE16C4526}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{51F4DBDB-4921-358C-A2C7-89C81B0DB47D}.Debug|x64.ActiveCfg = Debug|x64
		{0FE66756-3003-3578-B36C-1AEEE16C4526}.Debug|x64.ActiveCfg = Debug|x64
		{0FE66756-3003-3578-B36C-1AEEE16C4526}.Debug|x64.Build.0 = Debug|x64
		{4DF95153-C904-3ACE-95EB-E87D953A279D}.Debug|x64.ActiveCfg = Debug|x64
		{4DF95153-C904-3ACE-95EB-E87D953A279D}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{51F4DBDB-4921-358C-A2C7-89C81B0DB47D} = {EF929AE0-7F2A-3B59-88FB-44CA75684298}
		{0FE66756-3003-3578-B36C-1AEEE16C4526} = {EF929AE0-7F2A-3B59-88FB-44CA75684298}
		{47B3954E-FCA8-3E70-8FC1-513443BE52A3} = {FF6D2FB8-126B-3D1D-8E46-CB7F5CA18917}
		{4DF95153-C904-3ACE-95EB-E87D953A279D} = {47B3954E-FCA8-3E70-8FC1-513443BE52A3}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2CD10293-917E-36C7-9B1F-DC0E5385E925}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
