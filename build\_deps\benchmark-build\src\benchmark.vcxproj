﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A1D8632E-211C-3C47-B2E0-4918C6C5A60B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>benchmark</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\Projects\Code\AI Agent\database_v11.5\build\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">benchmark.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">benchmark</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\Projects\Code\AI Agent\database_v11.5\build\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">benchmark.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">benchmark</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\Projects\Code\AI Agent\database_v11.5\build\lib\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">benchmark.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">benchmark</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\Projects\Code\AI Agent\database_v11.5\build\lib\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">benchmark.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">benchmark</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;BENCHMARK_STATIC_DEFINE;_CRT_SECURE_NO_WARNINGS;HAVE_STD_REGEX;HAVE_STEADY_CLOCK;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;BENCHMARK_STATIC_DEFINE;_CRT_SECURE_NO_WARNINGS;HAVE_STD_REGEX;HAVE_STEADY_CLOCK;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BENCHMARK_STATIC_DEFINE;_CRT_SECURE_NO_WARNINGS;HAVE_STD_REGEX;HAVE_STEADY_CLOCK;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BENCHMARK_STATIC_DEFINE;_CRT_SECURE_NO_WARNINGS;HAVE_STD_REGEX;HAVE_STEADY_CLOCK;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BENCHMARK_STATIC_DEFINE;_CRT_SECURE_NO_WARNINGS;HAVE_STD_REGEX;HAVE_STEADY_CLOCK;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BENCHMARK_STATIC_DEFINE;_CRT_SECURE_NO_WARNINGS;HAVE_STD_REGEX;HAVE_STEADY_CLOCK;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BENCHMARK_STATIC_DEFINE;_CRT_SECURE_NO_WARNINGS;HAVE_STD_REGEX;HAVE_STEADY_CLOCK;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BENCHMARK_STATIC_DEFINE;_CRT_SECURE_NO_WARNINGS;HAVE_STD_REGEX;HAVE_STEADY_CLOCK;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5" "-BE:/Projects/Code/AI Agent/database_v11.5/build" --check-stamp-file "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-build/src/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\WriteBasicConfigVersionFile.cmake;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\Config.cmake.in;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\benchmark.pc.in;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\benchmark_main.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5" "-BE:/Projects/Code/AI Agent/database_v11.5/build" --check-stamp-file "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-build/src/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\WriteBasicConfigVersionFile.cmake;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\Config.cmake.in;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\benchmark.pc.in;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\benchmark_main.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5" "-BE:/Projects/Code/AI Agent/database_v11.5/build" --check-stamp-file "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-build/src/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\WriteBasicConfigVersionFile.cmake;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\Config.cmake.in;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\benchmark.pc.in;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\benchmark_main.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5" "-BE:/Projects/Code/AI Agent/database_v11.5/build" --check-stamp-file "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-build/src/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\WriteBasicConfigVersionFile.cmake;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\Config.cmake.in;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\benchmark.pc.in;E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\cmake\benchmark_main.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include\benchmark\benchmark.h" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\include\benchmark\export.h" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\arraysize.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark.cc">
      <ScanSourceforModuleDependencies Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</ScanSourceforModuleDependencies>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);BENCHMARK_VERSION="v1.9.4"</PreprocessorDefinitions>
      <ScanSourceforModuleDependencies Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</ScanSourceforModuleDependencies>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);BENCHMARK_VERSION="v1.9.4"</PreprocessorDefinitions>
      <ScanSourceforModuleDependencies Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</ScanSourceforModuleDependencies>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(PreprocessorDefinitions);BENCHMARK_VERSION="v1.9.4"</PreprocessorDefinitions>
      <ScanSourceforModuleDependencies Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</ScanSourceforModuleDependencies>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(PreprocessorDefinitions);BENCHMARK_VERSION="v1.9.4"</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_api_internal.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_api_internal.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_name.cc" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_register.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_register.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_runner.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\benchmark_runner.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\check.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\check.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\colorprint.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\colorprint.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\commandlineflags.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\commandlineflags.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\complexity.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\complexity.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\console_reporter.cc" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\counter.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\counter.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\csv_reporter.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\cycleclock.h" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\internal_macros.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\json_reporter.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\log.h" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\mutex.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\perf_counters.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\perf_counters.h" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\re.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\reporter.cc" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\statistics.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\statistics.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\string_util.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\string_util.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\sysinfo.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\thread_manager.h" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\thread_timer.h" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\timers.cc" />
    <ClInclude Include="E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src\src\timers.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\Projects\Code\AI Agent\database_v11.5\build\ZERO_CHECK.vcxproj">
      <Project>{58B244FA-5339-396D-8649-06E194B8647E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>