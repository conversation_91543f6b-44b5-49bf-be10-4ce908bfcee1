^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\45BF86DC109FDAADAE6C3E373EA767B3\BENCHMARK-POPULATE-MKDIR.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/tmp/benchmark-populate-mkdirs.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/src/benchmark-populate-stamp/Debug/benchmark-populate-mkdir"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\45BF86DC109FDAADAE6C3E373EA767B3\BENCHMARK-POPULATE-DOWNLOAD.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -P "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/tmp/benchmark-populate-gitclone.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/src/benchmark-populate-stamp/Debug/benchmark-populate-download"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\45BF86DC109FDAADAE6C3E373EA767B3\BENCHMARK-POPULATE-UPDATE.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-src"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -Dcan_fetch=YES -P "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/tmp/benchmark-populate-gitupdate.cmake"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\45BF86DC109FDAADAE6C3E373EA767B3\BENCHMARK-POPULATE-PATCH.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/src/benchmark-populate-stamp/Debug/benchmark-populate-patch"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\45BF86DC109FDAADAE6C3E373EA767B3\BENCHMARK-POPULATE-CONFIGURE.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-build"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/src/benchmark-populate-stamp/Debug/benchmark-populate-configure"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\45BF86DC109FDAADAE6C3E373EA767B3\BENCHMARK-POPULATE-BUILD.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-build"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/src/benchmark-populate-stamp/Debug/benchmark-populate-build"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\45BF86DC109FDAADAE6C3E373EA767B3\BENCHMARK-POPULATE-INSTALL.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-build"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/src/benchmark-populate-stamp/Debug/benchmark-populate-install"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\45BF86DC109FDAADAE6C3E373EA767B3\BENCHMARK-POPULATE-TEST.RULE
setlocal
cd "E:\Projects\Code\AI Agent\database_v11.5\build\_deps\benchmark-build"
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/src/benchmark-populate-stamp/Debug/benchmark-populate-test"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\F2A2FFD668A04D71E39FA8FAD3ADF21C\BENCHMARK-POPULATE-COMPLETE.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/CMakeFiles/Debug"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/CMakeFiles/Debug/benchmark-populate-complete"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/benchmark-populate-prefix/src/benchmark-populate-stamp/Debug/benchmark-populate-done"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKEFILES\8BD7B8936D85EF22DFD1B306C53425EB\BENCHMARK-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild" "-BE:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild" --check-stamp-file "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
