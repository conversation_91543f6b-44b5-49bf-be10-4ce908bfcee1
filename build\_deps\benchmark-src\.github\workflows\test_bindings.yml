name: test-bindings

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  CMAKE_GENERATOR: Ninja

jobs:
  python_bindings:
    name: Test GBM Python ${{ matrix.python-version }} bindings on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, macos-latest, windows-latest ]
        python-version: [ "3.10", "3.11", "3.12", "3.13" ]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install GBM Python bindings on ${{ matrix.os }}
        run: python -m pip install .
      - name: Run example on ${{ matrix.os }} under Python ${{ matrix.python-version }}
        run: python bindings/python/google_benchmark/example.py
