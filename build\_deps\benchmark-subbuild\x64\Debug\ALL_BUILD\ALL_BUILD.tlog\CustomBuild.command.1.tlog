^E:\PROJECTS\CODE\AI AGENT\DATABASE_V11.5\BUILD\_DEPS\BENCHMARK-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild" "-BE:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild" --check-stamp-file "E:/Projects/Code/AI Agent/database_v11.5/build/_deps/benchmark-subbuild/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
